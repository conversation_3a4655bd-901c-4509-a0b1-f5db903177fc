#include "envdataparser.h"
#include "temphumiparser.h"
#include "sf6parser.h"
#include "log.h"

EnvDataParser::EnvDataParser(QByteArray &data)
: m_data(data)
{

}

EnvDataParser::~EnvDataParser()
{

}

bool EnvDataParser::parse()
{
    bool bRet = false;

    if (m_data.length() < EnvDataParse::MIN_DATA_LENGTH)
    {
        logError("data length is less than min data length,data size:") << m_data.size();
        return bRet;
    }

    QDataStream dataStream(&m_data, QIODevice::ReadOnly);
    dataStream.setByteOrder(QDataStream::LittleEndian);

    //版本号
    quint8 version;
    dataStream >> version;
    m_header.m_version = static_cast<EnvDataParse::EnvDataVersion>(version);
    if (m_header.m_version != EnvDataParse::EnvDataVersion::EnvDataVersion_V2)
    {
        logError("version is not supported,version:") << version;
        return bRet;
    }

    //数据类型
    quint8 dataType;
    dataStream >> dataType;
    m_header.m_dataType = static_cast<EnvDataParse::EnvDataType>(dataType);

    //数据长度
    dataStream >> m_header.m_dataLength;

    //数据个数
    dataStream >> m_header.m_dataCount;

    //电池电压
    dataStream >> m_header.m_batteryVoltage;

    //数据列表
    m_dataList.resize(m_header.m_dataLength - EnvDataParse::MIN_DATA_LENGTH);
    dataStream.readRawData(m_dataList.data(), m_header.m_dataLength - EnvDataParse::MIN_DATA_LENGTH);

    // 解析数据
    EnvDataParse::EnvDataBaseParser* parser = nullptr;
    switch (m_header.m_dataType)
    {
        case EnvDataParse::EnvDataType::TemperatureAndHumidity:
            parser = new EnvDataParse::TempHumiParser(m_dataList, m_header.m_dataCount);
            break;
        case EnvDataParse::EnvDataType::SF6:
            parser = new EnvDataParse::SF6Parser(m_dataList, m_header.m_dataCount);
            break;
        default:
            logError("data type is not supported,data type:") << dataType;
            return bRet;
    }

    if (parser)
    {
        bRet = parser->parse();
        if (bRet)
        {
            m_envDataInfo.m_dataType = m_header.m_dataType;
            m_envDataInfo.m_dataList = parser->getEnvDataList();
        }
        delete parser;
        parser = nullptr;
    }

    logTrace("parse env data,version:") << version << ",data type:" << dataType << ",data count:" << m_header.m_dataCount << ",data length:" << m_header.m_dataLength << ",battery voltage:" << m_header.m_batteryVoltage;

    return bRet;
}
