#ifndef WEBREQUESTHANDLERFACTORY_H
#define WEBREQUESTHANDLERFACTORY_H
#include <memory>
#include <QString>

namespace WebRequestHandle {
    class IWebRequestHandler;
}
class WebRequestHandlerFactory
{
public:
    WebRequestHandlerFactory();

    //根据请求路径创建请求处理类
    static std::unique_ptr<WebRequestHandle::IWebRequestHandler> createHandler(const QString& path);
};

#endif // WEBREQUESTHANDLERFACTORY_H
