#include "outwardprotocolconfigsavehandler.h"
#include "configservice/outwardprotocolconfig/outwardprotocolconfig.h"
#include "configservice.h"
#include "log.h"
#include <QJsonDocument>

enum SaveConfigErrorCode
{
    WEB_REQUEST_ERROR_CODE_CONFIG_NOT_EXIST = 1000, //配置不存在
    WEB_REQUEST_ERROR_CODE_SAVE_FAILED = 1001,      //保存失败
};

WebRequestHandle::OutwardProtocolConfigSaveHandler::OutwardProtocolConfigSaveHandler()
{

}

bool WebRequestHandle::OutwardProtocolConfigSaveHandler::parseRequestParam(const HttpRequest &request)
{   
    // 获取协议类型
    m_eProtocolType = OutwardProtocolDefine::UNKNOWN_PROTOCOL;

    const QString strCommProtocolType =  request.getParameter(STR_COMM_PROTOCOL);

    if(strCommProtocolType.isEmpty())
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    m_eProtocolType = OutwardProtocolConfig::protocolTypeFromString(strCommProtocolType);
    if(OutwardProtocolDefine::UNKNOWN_PROTOCOL == m_eProtocolType)
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    //获取配置标识
    m_strConfigId = request.getParameter(STR_CONFIG_ID);
    if(m_strConfigId.isEmpty())
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    //获取业务协议
    m_strBusinessProtocol = request.getParameter(STR_BUSINESS_PROTOCOL);
    if(m_strBusinessProtocol.isEmpty())
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    //获取配置内容
    QJsonDocument configJsonDoc = QJsonDocument::fromJson(request.getParameter(STR_PROTOCOL_CONFIG));
    if(configJsonDoc.isNull() || !configJsonDoc.isObject())
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }
    const QJsonObject configObject = configJsonDoc.object();

    bool bRet = true;
    switch (m_eProtocolType) {
    case OutwardProtocolDefine::I2_PROTOCOL:
    {
        bRet = parseI2ProtocolConfig(configObject);
    }
        break;
    case OutwardProtocolDefine::IEC_61850_PROTOCOL:
    {
        bRet = parseIEC61850ProtocolConfig(request);
    }
        break;
    case OutwardProtocolDefine::IEC104_PROTOCOL:
    {
        bRet = parseIEC104ProtocolConfig(configObject);
    }
        break;
    case OutwardProtocolDefine::MODBUS_PROTOCOL:
    {
        bRet = parseModbusProtocolConfig(configObject);
    }
        break;
    default:
        break;
    }

    return bRet;
}

QJsonValue WebRequestHandle::OutwardProtocolConfigSaveHandler::getResponseResultData()
{
    return QJsonValue("");
}

bool WebRequestHandle::OutwardProtocolConfigSaveHandler::parseI2ProtocolConfig(const QJsonObject &configObject)
{
    const ConfigService& configService = ConfigService::instance();
    if(!configService.getOutwardProtocolConfig()->isI2ProtocolConfigExist(m_strConfigId))
    {
       setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_CONFIG_NOT_EXIST));
       return false;
    }

    OutwardProtocolDefine::I2ProtocolConfig config;
    config.eBusinessProtocolType = OutwardProtocolConfig::businessProtocolTypeFromStringForI2(m_strBusinessProtocol);
    config.strServerIP = configObject.value("serverIP").toString();
    config.usPort = configObject.value("port").toInt();
    config.strUrl = configObject.value("url").toString();
    config.strUser = configObject.value("user").toString();
    config.strPasswd = configObject.value("passwd").toString();
    config.nHeartInterval = configObject.value("heartInterval").toInt();
    config.nDataInterval = configObject.value("dataInterval").toInt();
    //to do 参数验证

    bool bRet = configService.getOutwardProtocolConfig()->saveI2ProtocolConfig(m_strConfigId, config);
    if(!bRet)
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_SAVE_FAILED));
    }

    return bRet;
}

bool WebRequestHandle::OutwardProtocolConfigSaveHandler::parseIEC104ProtocolConfig(const QJsonObject &configObject)
{
    const ConfigService& configService = ConfigService::instance();
    if(!configService.getOutwardProtocolConfig()->isIEC104ProtocolConfigExist(m_strConfigId))
    {
       setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_CONFIG_NOT_EXIST));
       return false;
    }

    OutwardProtocolDefine::IEC104ProtocolConfig config;
    config.eIEC104BusinessProtocolType = OutwardProtocolConfig::businessProtocolTypeFromStringForIEC104(m_strBusinessProtocol);
    config.strServerIP = configObject.value("serverIP").toString();
    config.usPort = configObject.value("port").toInt();
    config.K = configObject.value("K").toInt();
    config.W = configObject.value("W").toInt();
    config.t0 = configObject.value("t0").toInt();;
    config.t1 = configObject.value("t1").toInt();
    config.t2 = configObject.value("t2").toInt();
    config.t3 = configObject.value("t3").toInt();
    //to do 参数验证

    bool bRet = configService.getOutwardProtocolConfig()->saveIEC104ProtocolConfig(m_strConfigId, config);
    if(!bRet)
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_SAVE_FAILED));
    }

    return bRet;
}

bool WebRequestHandle::OutwardProtocolConfigSaveHandler::parseIEC61850ProtocolConfig(const HttpRequest &request)
{
    const ConfigService& configService = ConfigService::instance();
    if(!configService.getOutwardProtocolConfig()->isIEC61850ProtocolConfigExist(m_strConfigId))
    {
       setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_CONFIG_NOT_EXIST));
       return false;
    }

    const QByteArray mmsFieldName = "mms";
    const QByteArray startupFieldName = "startup";
    const QByteArray usermapFieldName = "usermap";
    const QByteArray cidFieldName = "cid";

    // 获取程序运行目录的路径
    QString appDirPath = QCoreApplication::applicationDirPath();
    OutwardProtocolDefine::IEC61850ProtocolConfig config;
    config.eBusinessProtocolType = OutwardProtocolConfig::businessProtocolTypeFromStringForIEC61850(m_strBusinessProtocol);

    QString filePath;
    //mms.ini
    filePath = appDirPath + QDir::separator() + request.getParameter(mmsFieldName);
    if(!saveTemporaryFileToPath(request.getUploadedFile(mmsFieldName), filePath))
    {
        logError("saveTemporaryFileToPath failed: ") << filePath;
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_SAVE_FAILED));
        return false;
    }
    config.strMmsFilePath = filePath;

    //startup.cfg
    filePath = appDirPath + QDir::separator() + request.getParameter(startupFieldName);
    if(!saveTemporaryFileToPath(request.getUploadedFile(startupFieldName), filePath))
    {
        logError("saveTemporaryFileToPath failed: ") << filePath;
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_SAVE_FAILED));
        return false;
    }
    config.strStartupFilePath = filePath;

    //usermap.cfg
    filePath = appDirPath + QDir::separator() + request.getParameter(usermapFieldName);
    if(!saveTemporaryFileToPath(request.getUploadedFile(usermapFieldName), filePath))
    {
        logError("saveTemporaryFileToPath failed: ") << filePath;
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_SAVE_FAILED));
        return false;
    }
    config.strUsermapFilePath = filePath;

    //cid
    filePath = appDirPath + QDir::separator() + request.getParameter(cidFieldName);
    if(!saveTemporaryFileToPath(request.getUploadedFile(cidFieldName), filePath))
    {
        logError("saveTemporaryFileToPath failed: ") << filePath;
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_SAVE_FAILED));
        return false;
    }
    config.strCidFilePath = filePath;

    bool bRet = configService.getOutwardProtocolConfig()->saveIEC61850ProtocolConfig(m_strConfigId, config);
    if(!bRet)
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_SAVE_FAILED));
    }

    return bRet;
}

bool WebRequestHandle::OutwardProtocolConfigSaveHandler::saveTemporaryFileToPath(QTemporaryFile *tempFile, const QString &targetPath)
{
    logInfo("saveTemporaryFileToPath: ") << targetPath;

    if(!tempFile)
    {
        logError("tempFile is null");
        return false;
    }

    // 确保临时文件存在并且已经打开
    if (!tempFile->exists() || !tempFile->isOpen()) {
        logError("temporary file does not exist or is not open");
        return false;
    }

    // 创建一个QFile对象，用于目标文件
    QFile targetFile(targetPath);

    // 尝试以写入模式打开目标文件
    if (!targetFile.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
       logError("Failed to open target file");
        return false;
    }

    // 读取临时文件内容并写入目标文件
    QByteArray data = tempFile->readAll();
    targetFile.write(data);

    // 关闭文件
    targetFile.close();

    if(targetFile.error() != QFile::NoError)
    {
        logError("Failed to write to target file");
        return false;
    }

    logInfo("Successfully saved temporary file to target path: ") << targetPath;

    return true;
}

bool WebRequestHandle::OutwardProtocolConfigSaveHandler::parseModbusProtocolConfig(const QJsonObject &configObject)
{
    const ConfigService& configService = ConfigService::instance();
    if(!configService.getOutwardProtocolConfig()->isModbusProtocolConfigExist(m_strConfigId))
    {
       setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_CONFIG_NOT_EXIST));
       return false;
    }

    OutwardProtocolDefine::ModbusProtocolConfig config;
    config.eBusinessProtocolType = OutwardProtocolConfig::businessProtocolTypeFromStringForModbus(m_strBusinessProtocol);
    config.strSerialPort = configObject.value("serialPort").toString();
    config.baudRate = configObject.value("baudRate").toInt();
    config.slaveAddress = configObject.value("slaveAddress").toInt();
    config.isEnableDbToDdm = configObject.value("isEnableDbToDbm").toBool();
    //to do 参数验证

    bool bRet = configService.getOutwardProtocolConfig()->saveModbusProtocolConfig(m_strConfigId, config);
    if(!bRet)
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_SAVE_FAILED));
    }

    return bRet;
}
