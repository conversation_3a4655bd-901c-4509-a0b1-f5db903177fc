#include "sf6parser.h"
#include "envdatadefine.h"
#include "log.h"

namespace EnvDataParse {

SF6Parser::SF6Parser(QByteArray &data, quint8 dataCount)
    : EnvDataBaseParser(data, dataCount)
{

}

SF6Parser::~SF6Parser()
{

}

bool SF6Parser::parse()
{
    bool bRet = false;

    if (m_data.size() < SF6_DATA_LENGTH * m_dataCount)
    {
        logError("data length is not enough,data length:") << m_data.size() << ",data count:" << m_dataCount;
        return bRet;
    }

    QDataStream dataStream(&m_data, QIODevice::ReadOnly);
    dataStream.setByteOrder(QDataStream::LittleEndian);

    for (quint8 i = 0; i < m_dataCount; ++i)
    {
        SF6Data data;

        // 数据采集时间 (6字节)
        QByteArray dataTimeArray;
        dataTimeArray.resize(6);
        dataStream.readRawData(dataTimeArray.data(), 6);
        data.m_dataTime = convertByteArrayToDateTime(dataTimeArray);

        // SF6气体密度 (P20) - 2字节 int16，单位：0.01MPa
        qint16 sf6Density = 0;
        dataStream >> sf6Density;
        data.m_sf6Density = sf6Density * 0.01;

        // SF6气体温度 - 2字节 int16，单位：0.01°C
        qint16 sf6Temperature = 0;
        dataStream >> sf6Temperature;
        data.m_sf6Temperature = sf6Temperature * 0.01;

        // SF6气体压力 - 2字节 int16，单位：0.01MPa
        qint16 sf6Pressure = 0;
        dataStream >> sf6Pressure;
        data.m_sf6Pressure = sf6Pressure * 0.01;

        // 环境温度 - 2字节 int16，单位：0.01°C
        qint16 envTemperature = 0;
        dataStream >> envTemperature;
        data.m_envTemperature = envTemperature * 0.01;

        // 超级电容电压 - 2字节 uint16，单位：mV
        quint16 superCapVoltage = 0;
        dataStream >> superCapVoltage;
        data.m_superCapVoltage = superCapVoltage;

        // 告警状态标识 - 1字节 uint8
        quint8 alarmStatus = 0;
        dataStream >> alarmStatus;
        data.m_alarmStatus = alarmStatus;

        m_envDataList.append(QVariant::fromValue(data));
        logTrace("parse SF6 data,data time:" << data.m_dataTime 
                 << ",SF6 density:" << data.m_sf6Density 
                 << ",SF6 temperature:" << data.m_sf6Temperature 
                 << ",SF6 pressure:" << data.m_sf6Pressure 
                 << ",env temperature:" << data.m_envTemperature 
                 << ",super cap voltage:" << data.m_superCapVoltage 
                 << ",alarm status:" << data.m_alarmStatus);
    }

    if (m_envDataList.size() == m_dataCount)
    {
        bRet = true;
    }

    return bRet;
}

} // namespace EnvDataParse
