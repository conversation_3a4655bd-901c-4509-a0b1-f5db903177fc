#include "webrequesthandlerfactory.h"
#include "iwebrequesthandler.h"
#include "log.h"
#include "outwardprotocolconfig/businessprotocolqueryhandler.h"
#include "outwardprotocolconfig/outwardprotocolconfigqueryhandler.h"
#include "outwardprotocolconfig/outwardprotocolconfigenablehandler.h"
#include "outwardprotocolconfig/outwardprotocolconfigdisablehandler.h"
#include "outwardprotocolconfig/outwardprotocolconfigsavehandler.h"

WebRequestHandlerFactory::WebRequestHandlerFactory()
{

}

std::unique_ptr<WebRequestHandle::IWebRequestHandler> WebRequestHandlerFactory::createHandler(const QString &path)
{
    std::unique_ptr<WebRequestHandle::IWebRequestHandler> requestHandler;

    if (path == "/getSupportedBusinessProtocols")
    {
        requestHandler.reset(new WebRequestHandle::BusinessProtocolQueryHandler);
    }
    else if(path == "/getOutwardProtocolConfig")
    {
        requestHandler.reset(new WebRequestHandle::OutwardProtocolConfigQueryHandler);
    }
    else if(path == "/saveOutwardProtocolConfig")
    {
        requestHandler.reset(new WebRequestHandle::OutwardProtocolConfigSaveHandler);
    }
    else if(path == "/enableOutwardProtocolConfig")
    {
        requestHandler.reset(new WebRequestHandle::OutwardProtocolConfigEnableHandler);
    }
    else if(path == "/disableOutwardProtocolConfig")
    {
        requestHandler.reset(new WebRequestHandle::OutwardProtocolConfigDisableHandler);
    }
    else
    {
        logError("Unknown path: " << path);
    }

    return requestHandler;
}
