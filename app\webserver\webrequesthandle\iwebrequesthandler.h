#ifndef IWEBREQUESTHANDLER_H
#define IWEBREQUESTHANDLER_H

#include "httprequesthandler.h"
#include <QJsonObject>

#define STR_COMM_PROTOCOL "communicationProtocol" //通信协议类型
#define STR_ID "id"     //序号
#define STR_CONFIG_ID "configID"    //配置标识
#define STR_CONFIG_STATUS "configStatus" //配置状态
#define STR_BUSINESS_PROTOCOL "businessProtocol" //业务协议
#define STR_CONFIG_DETAILS "configDetails"      //配置详情
#define STR_PROTOCOL_CONFIG "protocolConfig"    //协议配置


namespace WebRequestHandle {
    //web请求错误码
    enum WebRequestErrorCode
    {
        WEB_REQUEST_ERROR_CODE_SUCCESS = 0, //成功
        WEB_REQUEST_ERROR_CODE_PARAM_ERROR = 101    //参数错误
    };


class IWebRequestHandler
{
public:
    IWebRequestHandler();
    ~IWebRequestHandler();

    //处理请求业务
    virtual void handle(HttpRequest &request, HttpResponse &response);

protected:
    //解析请求参数
    virtual bool parseRequestParam(const HttpRequest &request) = 0;

    //获取请求响应数据
    virtual QJsonValue getResponseResultData() = 0;

    //发送通用格式响应数据
    void sendResponseData(HttpResponse& response, const QJsonValue &resultData);

    //设置响应错误码
    void setResponseErrorCode(int errorCode);

private:
    int m_iErrorCode; //错误码
};

} // namespace WebRequestHandle


#endif // IWEBREQUESTHANDLER_H
