#include "webrouter.h"
#include "iwebrequesthandler.h"
#include "webrequesthandlerfactory.h"
#include "log.h"

WebRouter::WebRouter()
{
    initHandlers();
}

bool WebRouter::handleRequest(const QString &path, HttpRequest &request, HttpResponse &response)
{
    bool bRet = true;

    logTrace("WebRouter::handleRequest---") << path;

    if (m_requestHandlers.find(path) != m_requestHandlers.end())
    {
        m_requestHandlers[path]->handle(request, response);
    }
    else
    {
        logError("WebRouter::handleRequest failed to find handler for path: " << path);
        bRet = false;
    }

    return bRet;
}

void WebRouter::initHandlers()
{
    registerHandler("/getSupportedBusinessProtocols");
    registerHandler("/getOutwardProtocolConfig");
    registerHandler("/saveOutwardProtocolConfig");
    registerHandler("/enableOutwardProtocolConfig");
    registerHandler("/disableOutwardProtocolConfig");
}

void WebRouter::registerHandler(const QString &path)
{
    // 检查是否已存在
    if(m_requestHandlers.find(path) != m_requestHandlers.end())
    {
        logWarnning("WebRouter::registerHandler path already exists: " << path);
        return;
    }

    std::unique_ptr<WebRequestHandle::IWebRequestHandler> upRequestHandler = WebRequestHandlerFactory::createHandler(path);

    if(!upRequestHandler)
    {
        logError("WebRouter::registerHandler failed to create handler for path: " << path);
        return;
    }

    m_requestHandlers.insert(std::make_pair(path, std::move(upRequestHandler)));
}
