#include "outwardprotocolconfigqueryhandler.h"
#include "configservice/outwardprotocolconfig/outwardprotocolconfig.h"
#include "configservice.h"
#include <QJsonArray>


WebRequestHandle::OutwardProtocolConfigQueryHandler::OutwardProtocolConfigQueryHandler()
{

}

bool WebRequestHandle::OutwardProtocolConfigQueryHandler::parseRequestParam(const HttpRequest &request)
{
    m_eProtocolType = OutwardProtocolDefine::UNKNOWN_PROTOCOL;

    const QString strCommProtocolType =  request.getParameter(STR_COMM_PROTOCOL);

    if(strCommProtocolType.isEmpty())
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    m_eProtocolType = OutwardProtocolConfig::protocolTypeFromString(strCommProtocolType);
    if(OutwardProtocolDefine::UNKNOWN_PROTOCOL == m_eProtocolType)
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    return true;
}

QJsonValue WebRequestHandle::OutwardProtocolConfigQueryHandler::getResponseResultData()
{
    QJsonObject result;
    QJsonArray protocolConfigs;

    switch (m_eProtocolType) {
    case OutwardProtocolDefine::I2_PROTOCOL:
    {
        protocolConfigs = getI2ProtocolConfig();
    }
        break;
    case OutwardProtocolDefine::IEC_61850_PROTOCOL:
    {
        protocolConfigs = getIEC61850ProtocolConfig();
    }
        break;
    case OutwardProtocolDefine::IEC104_PROTOCOL:
    {
        protocolConfigs = getIEC104ProtocolConfig();
    }
        break;
    case OutwardProtocolDefine::MODBUS_PROTOCOL:
    {
        protocolConfigs = getModbusProtocolConfig();
    }
        break;
    default:
        break;
    }

    result.insert(STR_PROTOCOL_CONFIG, protocolConfigs);

    return result;
}

QJsonArray WebRequestHandle::OutwardProtocolConfigQueryHandler::getI2ProtocolConfig()
{
    QJsonArray protocolConfigs;

    const ConfigService& configService = ConfigService::instance();

    QList<OutwardProtocolDefine::I2ProtocolConfig> configList = configService.getOutwardProtocolConfig()->getI2ProtocolConfig();

    for(int i = 0; i < configList.size(); ++i)
    {
        const OutwardProtocolDefine::I2ProtocolConfig& config = configList.at(i);

        QJsonObject protocolConfig, configDetails;
        protocolConfig.insert(STR_ID, i);
        protocolConfig.insert(STR_COMM_PROTOCOL, OutwardProtocolConfig::protocolTypeToString(m_eProtocolType));
        protocolConfig.insert(STR_CONFIG_ID, config.strConfigId);
        protocolConfig.insert(STR_BUSINESS_PROTOCOL, OutwardProtocolConfig::businessProtocolTypeToStringForI2(config.eBusinessProtocolType));
        protocolConfig.insert(STR_CONFIG_STATUS, static_cast<int>(config.eConfigStatus));

        configDetails.insert("serverIP", config.strServerIP);
        configDetails.insert("port", config.usPort);
        configDetails.insert("url", config.strUrl);
        configDetails.insert("user", config.strUser);
        configDetails.insert("passwd", config.strPasswd);
        configDetails.insert("heartInterval", static_cast<int>(config.nHeartInterval));
        configDetails.insert("dataInterval", static_cast<int>(config.nDataInterval));

        protocolConfig.insert(STR_CONFIG_DETAILS, configDetails);

        protocolConfigs.append(protocolConfig);
    }

    return protocolConfigs;
}

QJsonArray WebRequestHandle::OutwardProtocolConfigQueryHandler::getIEC104ProtocolConfig()
{
    QJsonArray protocolConfigs;

    const ConfigService& configService = ConfigService::instance();

    QList<OutwardProtocolDefine::IEC104ProtocolConfig> configList = configService.getOutwardProtocolConfig()->getIEC104ProtocolConfig();

    for(int i = 0; i < configList.size(); ++i)
    {
        const OutwardProtocolDefine::IEC104ProtocolConfig& config = configList.at(i);

        QJsonObject protocolConfig, configDetails;
        protocolConfig.insert(STR_ID, i);
        protocolConfig.insert(STR_COMM_PROTOCOL, OutwardProtocolConfig::protocolTypeToString(m_eProtocolType));
        protocolConfig.insert(STR_CONFIG_ID, config.strConfigId);
        protocolConfig.insert(STR_BUSINESS_PROTOCOL, OutwardProtocolConfig::businessProtocolTypeToStringForIEC104(config.eIEC104BusinessProtocolType));
        protocolConfig.insert(STR_CONFIG_STATUS, static_cast<int>(config.eConfigStatus));

        configDetails.insert("serverIP", config.strServerIP);
        configDetails.insert("port", config.usPort);
        configDetails.insert("K", static_cast<int>(config.K));
        configDetails.insert("W", static_cast<int>(config.W));
        configDetails.insert("t0", static_cast<int>(config.t0));
        configDetails.insert("t1", static_cast<int>(config.t1));
        configDetails.insert("t2", static_cast<int>(config.t2));
        configDetails.insert("t3", static_cast<int>(config.t3));

        protocolConfig.insert(STR_CONFIG_DETAILS, configDetails);
        protocolConfigs.append(protocolConfig);
    }

    return protocolConfigs;
}

QJsonArray WebRequestHandle::OutwardProtocolConfigQueryHandler::getIEC61850ProtocolConfig()
{
    QJsonArray protocolConfigs;

    const ConfigService& configService = ConfigService::instance();

    QList<OutwardProtocolDefine::IEC61850ProtocolConfig> configList = configService.getOutwardProtocolConfig()->getIEC61850ProtocolConfig();

    for(int i = 0; i < configList.size(); ++i)
    {
        const OutwardProtocolDefine::IEC61850ProtocolConfig& config = configList.at(i);

        QJsonObject protocolConfig, configDetails;
        protocolConfig.insert(STR_ID, i);
        protocolConfig.insert(STR_COMM_PROTOCOL, OutwardProtocolConfig::protocolTypeToString(m_eProtocolType));
        protocolConfig.insert(STR_CONFIG_ID, config.strConfigId);
        protocolConfig.insert(STR_BUSINESS_PROTOCOL, OutwardProtocolConfig::businessProtocolTypeToStringForIEC61850(config.eBusinessProtocolType));
        protocolConfig.insert(STR_CONFIG_STATUS, static_cast<int>(config.eConfigStatus));

        configDetails.insert("mms", config.strMmsFilePath);
        configDetails.insert("startup", config.strStartupFilePath);
        configDetails.insert("usermap", config.strUsermapFilePath);
        configDetails.insert("cid", config.strCidFilePath);

        protocolConfig.insert(STR_CONFIG_DETAILS, configDetails);
        protocolConfigs.append(protocolConfig);
    }

    return protocolConfigs;
}

QJsonArray WebRequestHandle::OutwardProtocolConfigQueryHandler::getModbusProtocolConfig()
{
    QJsonArray protocolConfigs;

    const ConfigService& configService = ConfigService::instance();

    QList<OutwardProtocolDefine::ModbusProtocolConfig> configList = configService.getOutwardProtocolConfig()->getModbusProtocolConfig();

    for(int i = 0; i < configList.size(); ++i)
    {
        const OutwardProtocolDefine::ModbusProtocolConfig& config = configList.at(i);

        QJsonObject protocolConfig, configDetails;
        protocolConfig.insert(STR_ID, i);
        protocolConfig.insert(STR_COMM_PROTOCOL, OutwardProtocolConfig::protocolTypeToString(m_eProtocolType));
        protocolConfig.insert(STR_CONFIG_ID, config.strConfigId);
        protocolConfig.insert(STR_BUSINESS_PROTOCOL, OutwardProtocolConfig::businessProtocolTypeToStringForModbus(config.eBusinessProtocolType));
        protocolConfig.insert(STR_CONFIG_STATUS, static_cast<int>(config.eConfigStatus));

        configDetails.insert("serialPort", config.strSerialPort);
        configDetails.insert("baudRate", config.baudRate);
        configDetails.insert("slaveAddress", config.slaveAddress);
        configDetails.insert("isEnableDbToDbm", config.isEnableDbToDdm);

        protocolConfig.insert(STR_CONFIG_DETAILS, configDetails);
        protocolConfigs.append(protocolConfig);
    }

    return protocolConfigs;
}
