#ifndef OUTWARDPROTOCOLCONFIGDISABLEHANDLER_H
#define OUTWARDPROTOCOLCONFIGDISABLEHANDLER_H

#include "webrequesthandle/iwebrequesthandler.h"
#include "configservice/outwardprotocolconfig/outwardprotocoldefine.h"

namespace WebRequestHandle {

class OutwardProtocolConfigDisableHandler : public IWebRequestHandler
{
public:
    OutwardProtocolConfigDisableHandler();

protected:
    //解析请求参数
    virtual bool parseRequestParam(const HttpRequest &request) override;

    //获取请求响应数据
    virtual QJsonValue getResponseResultData() override;

private:
    OutwardProtocolDefine::OutwardProtocolType m_eProtocolType; //通信协议类型
    QString m_strConfigId;     //配置标识
};

}
#endif // OUTWARDPROTOCOLCONFIGDISABLEHANDLER_H
