#include <QJsonDocument>
#include "iwebrequesthandler.h"

namespace WebRequestHandle {

IWebRequestHandler::IWebRequestHandler()
{

}

IWebRequestHandler::~IWebRequestHandler()
{

}

void IWebRequestHandler::handle(HttpRequest &request, HttpResponse &response)
{
    QJsonValue resultData("");
    setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_SUCCESS));

    if(parseRequestParam(request))
    {
        resultData = getResponseResultData();
    }

    sendResponseData(response, resultData);
}

void IWebRequestHandler::sendResponseData(HttpResponse &response, const QJsonValue &resultData)
{
    QJsonObject responseObject;
    responseObject.insert("success", !m_iErrorCode? true : false);
    responseObject.insert("errorCode", m_iErrorCode);
    responseObject.insert("errormsg", QString());
    responseObject.insert("result", resultData);


    response.write(QJsonDocument(responseObject).toJson());
}


void IWebRequestHandler::setResponseErrorCode(int errorCode)
{
    m_iErrorCode = errorCode;
}

}

