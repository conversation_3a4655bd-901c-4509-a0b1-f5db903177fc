#ifndef OUTWARDPROTOCOLCONFIGQUERYHANDLER_H
#define OUTWARDPROTOCOLCONFIGQUERYHANDLER_H

#include "webrequesthandle/iwebrequesthandler.h"
#include "configservice/outwardprotocolconfig/outwardprotocoldefine.h"

namespace WebRequestHandle {

//获取通信协议配置
class OutwardProtocolConfigQueryHandler : public IWebRequestHandler
{
public:
    OutwardProtocolConfigQueryHandler();

protected:
    //解析请求参数
    virtual bool parseRequestParam(const HttpRequest &request) override;

    //获取请求响应数据
    virtual QJsonValue getResponseResultData() override;

private:
    //获取I2协议配置信息
    QJsonArray getI2ProtocolConfig();

    //获取IEC104协议配置信息
    QJsonArray getIEC104ProtocolConfig();

    //获取IEC61850协议配置信息
    QJsonArray getIEC61850ProtocolConfig();
    
    //获取Modbus协议配置信息
    QJsonArray getModbusProtocolConfig();

private:
    OutwardProtocolDefine::OutwardProtocolType m_eProtocolType; //通信协议类型
};

}
#endif // OUTWARDPROTOCOLCONFIGQUERYHANDLER_H
