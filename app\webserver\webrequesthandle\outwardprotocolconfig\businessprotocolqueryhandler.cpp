#include "businessprotocolqueryhandler.h"
#include "configservice/outwardprotocolconfig/outwardprotocolconfig.h"
#include <QJsonArray>

WebRequestHandle::BusinessProtocolQueryHandler::BusinessProtocolQueryHandler()
{

}

bool WebRequestHandle::BusinessProtocolQueryHandler::parseRequestParam(const HttpRequest &request)
{
    m_eProtocolType = OutwardProtocolDefine::UNKNOWN_PROTOCOL;

    const QString strCommProtocolType =  request.getParameter(STR_COMM_PROTOCOL);

    if(strCommProtocolType.isEmpty())
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    m_eProtocolType = OutwardProtocolConfig::protocolTypeFromString(strCommProtocolType);
    if(OutwardProtocolDefine::UNKNOWN_PROTOCOL == m_eProtocolType)
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    return true;
}

QJsonValue WebRequestHandle::BusinessProtocolQueryHandler::getResponseResultData()
{
    QJsonObject result;
    QJsonArray businessProtocols;

    switch (m_eProtocolType)
    {
    case OutwardProtocolDefine::I2_PROTOCOL:
    {
        QList<OutwardProtocolDefine::I2BusinessProtocolType> busTypeList = OutwardProtocolConfig::getI2BusinessProtocolList();
        for(int i = 0; i < busTypeList.size(); ++i)
        {
            businessProtocols.append(OutwardProtocolConfig::businessProtocolTypeToStringForI2(busTypeList.at(i)));
        }
    }
        break;
    case OutwardProtocolDefine::IEC_61850_PROTOCOL:
    {
        QList<OutwardProtocolDefine::Iec61850BusinessProtocolType> busTypeList = OutwardProtocolConfig::getIec61850BusinessProtocolList();
        for(int i = 0; i < busTypeList.size(); ++i)
        {
            businessProtocols.append(OutwardProtocolConfig::businessProtocolTypeToStringForIEC61850(busTypeList.at(i)));
        }
    }
        break;
    case OutwardProtocolDefine::IEC104_PROTOCOL:
    {
        QList<OutwardProtocolDefine::Iec104BusinessProtocolType> busTypeList = OutwardProtocolConfig::getIec104BusinessProtocolList();
        for(int i = 0; i < busTypeList.size(); ++i)
        {
            businessProtocols.append(OutwardProtocolConfig::businessProtocolTypeToStringForIEC104(busTypeList.at(i)));
        }
    }
        break;
    case OutwardProtocolDefine::MODBUS_PROTOCOL:
    {
        QList<OutwardProtocolDefine::ModbusBusinessProtocolType> busTypeList = OutwardProtocolConfig::getModbusBusinessProtocolList();
        for(int i = 0; i < busTypeList.size(); ++i)
        {
            businessProtocols.append(OutwardProtocolConfig::businessProtocolTypeToStringForModbus(busTypeList.at(i)));
        }
    }
        break;

    default:
        break;
    }

    result.insert("businessProtocols", businessProtocols);

    return result;
}
