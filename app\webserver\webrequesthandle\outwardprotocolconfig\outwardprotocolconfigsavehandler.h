#ifndef OUTWARDPROTOCOLCONFIGSAVEHANDLER_H
#define OUTWARDPROTOCOLCONFIGSAVEHANDLER_H

#include "webrequesthandle/iwebrequesthandler.h"
#include "configservice/outwardprotocolconfig/outwardprotocoldefine.h"

namespace WebRequestHandle {

class OutwardProtocolConfigSaveHandler : public IWebRequestHandler
{
public:
    OutwardProtocolConfigSaveHandler();

protected:
//解析请求参数
virtual bool parseRequestParam(const HttpRequest &request) override;

//获取请求响应数据
virtual QJsonValue getResponseResultData() override;

private:
    //解析I2协议配置
    bool parseI2ProtocolConfig(const QJsonObject &configObject);
    //解析IEC104协议配置
    bool parseIEC104ProtocolConfig(const QJsonObject &configObject);
    //解析IEC61850协议配置
    bool parseIEC61850ProtocolConfig(const HttpRequest &request);

    //保存临时文件到指定路径
    bool saveTemporaryFileToPath(QTemporaryFile *tempFile, const QString &targetPath);

    //解析Modbus协议配置
    bool parseModbusProtocolConfig(const QJsonObject &configObject);


private:
    OutwardProtocolDefine::OutwardProtocolType m_eProtocolType; //通信协议类型
    QString m_strConfigId;     //配置标识
    QString m_strBusinessProtocol; //业务协议

};

}

#endif // OUTWARDPROTOCOLCONFIGSAVEHANDLER_H
