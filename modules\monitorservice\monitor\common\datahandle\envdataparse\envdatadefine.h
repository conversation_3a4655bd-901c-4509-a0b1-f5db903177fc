#ifndef ENVDATADATAPARSEDEFINE
#define ENVDATADATAPARSEDEFINE

#include <QDateTime>
#include <QObject>

namespace EnvDataParse
{
    //单个温湿度数据长度
    const quint8 TEMP_HUM_DATA_LENGTH = 10;   //6 字节时间 + 2 字节温度 + 2 字节湿度

    //单个SF6数据长度
    const quint8 SF6_DATA_LENGTH = 17;   //6 字节时间 + 2 字节SF6气体密度 + 2 字节SF6气体温度 + 2 字节SF6气体压力 + 2 字节环境温度 + 2 字节超级电容电压 + 1 字节告警状态标识

    //温湿度数据
    struct TemperatureAndHumidityData
    {
        QDateTime m_dataTime; //数据采集时间
        float m_temperature; //温度
        float m_humidity; //湿度
    };

    //SF6数据
    struct SF6Data
    {
        QDateTime m_dataTime; //数据采集时间
        float m_sf6Density; //SF6气体密度 (P20) 单位：0.01MPa
        float m_sf6Temperature; //SF6气体温度 单位：0.01°C
        float m_sf6Pressure; //SF6气体压力 单位：0.01MPa
        float m_envTemperature; //环境温度 单位：0.01°C
        quint16 m_superCapVoltage; //超级电容电压 单位：mV
        quint8 m_alarmStatus; //告警状态标识
    };

}
Q_DECLARE_METATYPE(EnvDataParse::TemperatureAndHumidityData)
Q_DECLARE_METATYPE(EnvDataParse::SF6Data)



#endif // ENVDATADATAPARSEDEFINE

