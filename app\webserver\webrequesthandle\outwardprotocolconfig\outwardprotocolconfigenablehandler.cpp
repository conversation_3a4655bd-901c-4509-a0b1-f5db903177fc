#include "outwardprotocolconfigenablehandler.h"
#include "configservice/outwardprotocolconfig/outwardprotocolconfig.h"
#include "configservice.h"
#include "outwardservice.h"

//启用配置错误码
enum WEB_REQUEST_ERROR_CODE_ENABLE_CONFIG
{
    CONFIG_NOT_EXIST = 1000 //配置不存在
};

WebRequestHandle::OutwardProtocolConfigEnableHandler::OutwardProtocolConfigEnableHandler()
{

}

bool WebRequestHandle::OutwardProtocolConfigEnableHandler::parseRequestParam(const HttpRequest &request)
{
    // 获取协议类型
    m_eProtocolType = OutwardProtocolDefine::UNKNOWN_PROTOCOL;

    const QString strCommProtocolType =  request.getParameter(STR_COMM_PROTOCOL);

    if(strCommProtocolType.isEmpty())
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    m_eProtocolType = OutwardProtocolConfig::protocolTypeFromString(strCommProtocolType);
    if(OutwardProtocolDefine::UNKNOWN_PROTOCOL == m_eProtocolType)
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    //获取配置标识
    m_strConfigId = request.getParameter(STR_CONFIG_ID);
    if(m_strConfigId.isEmpty())
    {
        setResponseErrorCode(static_cast<int>(WEB_REQUEST_ERROR_CODE_PARAM_ERROR));
        return false;
    }

    return true;
}

QJsonValue WebRequestHandle::OutwardProtocolConfigEnableHandler::getResponseResultData()
{
    QSharedPointer<OutwardProtocolConfig> spProtocolConfig = ConfigService::instance().getOutwardProtocolConfig();

    switch (m_eProtocolType) {
    case OutwardProtocolDefine::I2_PROTOCOL:
    {
        if(spProtocolConfig->isI2ProtocolConfigExist(m_strConfigId))
        {
             //先设置对应的协议为正在启用状态，启用是异步启动，防止前端直接查询到未启用状态
             //协议启用失败需要进行状态更新，防止一直在启用中状态

            //配置状态置为启用中
            spProtocolConfig->setI2ProtocolConfigStatus(m_strConfigId, OutwardProtocolDefine::OutwardProtocolStatus::PROTOCOL_STATUS_ENABLING);
            LOG_TRACE(0) << "1111";
            OutwardService::instance().startService(m_eProtocolType, m_strConfigId);
            LOG_TRACE(0) << "2222";
        }
        else
        {
            setResponseErrorCode(static_cast<int>(CONFIG_NOT_EXIST));
        }
    }
        break;
    case OutwardProtocolDefine::IEC_61850_PROTOCOL:
    {
        if(spProtocolConfig->isIEC61850ProtocolConfigExist(m_strConfigId))
        {
            //配置状态置为启用中
            spProtocolConfig->setIEC61850ProtocolConfigStatus(m_strConfigId, OutwardProtocolDefine::OutwardProtocolStatus::PROTOCOL_STATUS_ENABLING);
            OutwardService::instance().startService(m_eProtocolType, m_strConfigId);
        }
        else
        {
            setResponseErrorCode(static_cast<int>(CONFIG_NOT_EXIST));
        }
    }
        break;
    case OutwardProtocolDefine::IEC104_PROTOCOL:
    {
        if(spProtocolConfig->isIEC104ProtocolConfigExist(m_strConfigId))
        {
            //配置状态置为启用中
            spProtocolConfig->setIEC104ProtocolConfigStatus(m_strConfigId, OutwardProtocolDefine::OutwardProtocolStatus::PROTOCOL_STATUS_ENABLING);
            OutwardService::instance().startService(m_eProtocolType, m_strConfigId);
        }
        else
        {
            setResponseErrorCode(static_cast<int>(CONFIG_NOT_EXIST));
        }
    }
        break;
    case OutwardProtocolDefine::MODBUS_PROTOCOL:
    {
        if(spProtocolConfig->isModbusProtocolConfigExist(m_strConfigId))
        {
            //配置状态置为启用中
            spProtocolConfig->setModbusProtocolConfigStatus(m_strConfigId, OutwardProtocolDefine::OutwardProtocolStatus::PROTOCOL_STATUS_ENABLING);
            OutwardService::instance().startService(m_eProtocolType, m_strConfigId);
        }
        else
        {
            setResponseErrorCode(static_cast<int>(CONFIG_NOT_EXIST));
        }
    }
        break;
    default:
        break;
    }

    return QJsonValue("");
}
