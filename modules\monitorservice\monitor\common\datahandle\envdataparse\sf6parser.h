#ifndef ENVDATAPARSE_SF6PARSER_H
#define ENVDATAPARSE_SF6PARSER_H
#include "envdatabaseparser.h"

namespace EnvDataParse {

/**
 * @brief SF6解析类
 */
class SF6Parser : public EnvDataBaseParser
{
public:
    SF6Parser(QByteArray &data, quint8 dataCount);

    /**
     * @brief 析构函数
     */
    ~SF6Parser();
    
    /**
     * @brief 解析数据
     * @return 解析结果
     */
    bool parse() override;
    
};

} // namespace EnvDataParse

#endif // ENVDATAPARSE_SF6PARSER_H
