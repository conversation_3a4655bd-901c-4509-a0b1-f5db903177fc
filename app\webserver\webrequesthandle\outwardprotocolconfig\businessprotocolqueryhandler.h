#ifndef BUSINESSPROTOCOLQUERYHANDLER_H
#define BUSINESSPROTOCOLQUERYHANDLER_H

#include "webrequesthandle/iwebrequesthandler.h"
#include "configservice/outwardprotocolconfig/outwardprotocoldefine.h"

namespace WebRequestHandle {

class BusinessProtocolQueryHandler : public IWebRequestHandler
{
public:
    BusinessProtocolQueryHandler();

protected:
    //解析请求参数
    virtual bool parseRequestParam(const HttpRequest &request) override;

    //获取请求响应数据
    virtual QJsonValue getResponseResultData() override;

private:
    OutwardProtocolDefine::OutwardProtocolType m_eProtocolType; //通信协议类型
};

}


#endif // BUSINESSPROTOCOLQUERYHANDLER_H
